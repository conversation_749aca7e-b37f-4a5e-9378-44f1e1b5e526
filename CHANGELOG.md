# 更新日志 (CHANGELOG)

本文档记录项目的所有重要变更内容。

## [2025-08-12] - 最近一周更新总结

### 本周主要更新内容 📅

#### 2025-08-12
- **修复科目切换后保存时数据不正确的问题** - 解决 `/bookkeeping/view` 页面切换科目后保存时科目信息没有正确更新的问题
- **修复新增科目时辅助核算处理逻辑** - 解决重新新增科目时现有辅助核算信息没有被正确清除的问题，确保新增科目只保留新添加的辅助核算
- **优化新增科目上级科目选择和搜索功能** - 改进科目选择器的搜索和过滤功能
- **修复会计科目切换和辅助核算传参问题** - 解决 `/bookkeeping/review` 页面科目切换时辅助核算数据丢失和新增辅助核算数据传递失败的问题
- **修复新增辅助项目保存时数据传递问题** - 解决银行回单编辑中新增辅助项目保存失败的问题
- **修复银行回单编辑弹框辅助项目显示"无辅助项目"问题** - 优化辅助项目选择器的数据加载和显示逻辑

#### 2025-08-11
- **修复 review 页面保存时辅助核算数据传递问题** - 确保辅助核算信息正确传递到后端接口
- **修复新增科目名称不更新的问题** - 优化新增科目的数据处理和显示逻辑
- **实现银行回单对方科目和辅助项目的在线编辑** - 新增银行回单编辑功能，支持对方科目和辅助项目修改
- **修复废弃引用** - 清理无用的代码引用，优化代码结构

#### 2025-08-08 - 2025-08-09
- **优化科目映射参数获取逻辑** - 改进科目映射功能的数据处理
- **优化进项发票AI记账功能** - 支持按发票类型自动拆分处理

#### 2025-08-07
- **凭证编辑页面新增上一张/下一张凭证导航功能** - 提升凭证编辑的用户体验
- **优化凭证打印功能** - 直接使用接口返回的完整URL
- **优化原始凭证页面表格显示和金额统计功能** - 改进数据展示效果
- **实现顶部栏公司选择框复制按钮** - 内嵌显示，悬浮可见
- **优化科目映射功能** - 实现科目选择逻辑优化和分页修复

#### 2025-08-06
- **重构语音命令处理机制** - 使用ActionHandlers统一管理
- **科目映射配置** - 新增科目映射配置功能

#### 2025-08-05
- **支持勾选部分数据生成凭证功能** - 提升数据处理灵活性

### 本周重点改进 🚀
1. **科目切换保存问题修复** - 解决了科目切换后保存时数据不正确的核心问题
2. **新增科目辅助核算处理优化** - 解决了新增科目时辅助核算数据清理和重新添加的问题
3. **银行回单编辑功能完善** - 实现了完整的在线编辑流程
4. **新增科目功能优化** - 解决了上级科目选择限制问题
5. **辅助核算数据处理** - 修复了多个数据传递和显示问题
6. **用户体验提升** - 新增凭证导航、优化界面显示等

## [2025-08-12] - 修复科目切换后保存时数据不正确的问题

### 问题修复 🐛
- **科目切换后保存数据错误**：修复 `/bookkeeping/view` 页面切换科目后保存时，科目信息没有正确更新的问题
  - **根本原因**：`parseSubjectString` 方法在解析科目信息时，优先依赖科目文本字符串解析，而没有优先使用用户在 VoucherEditing 组件中选择的科目对象数据
  - **修复方案**：
    1. 在 `parseSubjectString` 方法开始处添加新的判断逻辑，优先检查科目对象是否包含完整的科目信息
    2. 智能提取科目信息：从科目对象的 `code`、`name`、`label` 等字段中提取科目代码、名称和ID
    3. 保持辅助核算兼容性：优先使用科目对象中的辅助核算信息，如果没有则保留原始数据中的辅助核算
    4. 保留新增科目标记：确保新增科目的相关标记得到正确传递

### 技术改进 ⚡
- **优先使用科目对象数据**：当科目对象包含 `value/id` 和 `code/label` 时，优先使用科目对象数据而不是文本解析
- **智能辅助核算处理**：根据科目对象和原始数据智能决定辅助核算信息的保留策略
- **向后兼容性保持**：修复保持了现有功能的向后兼容性，不影响其他功能

### 影响范围 📍
- `/bookkeeping/view` 页面的科目切换和保存功能
- 凭证审核时的科目数据处理逻辑
- 科目对象在不同组件间的数据传递
- 用户科目选择后的数据一致性保证

## [2025-08-12] - 修复新增科目时辅助核算处理逻辑

### 问题修复 🐛
- **新增科目辅助核算清理问题**：修复重新新增科目时现有辅助核算信息没有被正确清除的问题
  - **根本原因**：当用户选择新增科目时，系统没有自动清除之前科目的辅助核算信息，导致新增科目保留了错误的辅助核算数据
  - **修复方案**：
    1. 在 `VoucherEditing.vue` 的 `selectChange` 函数中添加新增科目检测逻辑
    2. 当检测到新增科目时自动清除现有辅助核算信息，只保留纯科目信息
    3. 优化 `listenAuxiliaryMitt` 函数的科目文本处理逻辑，支持辅助核算的去重和更新
    4. 在保存接口中优化新增科目的识别和处理逻辑

### 技术改进 ⚡
- **新增科目识别优化**：支持多种新增科目标记方式（`isNewSubject` 标记、`code === '404'` 等）
- **辅助核算数据清理**：确保新增科目只保留新添加的辅助核算信息，不保留原始数据
- **数据流程优化**：改进从科目选择到保存的完整数据处理流程

### 影响范围 📍
- 凭证录入页面的科目选择功能
- 辅助核算的添加和管理功能
- 凭证保存时的数据处理逻辑
- 新增科目的辅助核算支持

## [2025-08-12] - 修复会计科目切换和辅助核算传参问题

### 问题修复 🐛
- **科目切换时辅助核算数据丢失**：修复 `/bookkeeping/review` 页面中会计科目切换时辅助核算数据没有正确传入 `vouchers/update` 接口的问题
  - **根本原因**：`parseSubjectString` 函数在处理科目切换时，当新科目不支持辅助核算（`subjectUseAssistant` 为 false）时会强制清空辅助核算数据
  - **修复方案**：优化辅助核算数据保留逻辑，只有在科目对象明确指示不支持辅助核算时才清空数据，其他情况都保留原始辅助核算数据
  - **影响范围**：确保用户切换科目时不会意外丢失辅助核算信息

- **新增辅助核算数据传递失败**：修复新增辅助核算后数据没有正确传入 `vouchers/update` 接口 `auxiliary` 参数的问题
  - **根本原因**：`parseSubjectString` 函数没有检查 `subjectObject.assistantOptions` 中的辅助核算数据
  - **修复方案**：在 `parseSubjectString` 函数开头添加对 `subjectObject.assistantOptions` 的检查和处理逻辑
  - **数据流程**：`AddAuxiliaryPop` → `VoucherEditing.listenAuxiliaryMitt` → `subjectObject.assistantOptions` → `parseSubjectString` → `vouchers/update` API

### 技术优化 ⚡
- **增强 parseSubjectString 函数**：添加对科目对象中辅助核算选项的支持
- **优化辅助核算数据保留策略**：采用更智能的数据保留逻辑，避免意外数据丢失
- **完善调试日志**：添加详细的调试信息，便于问题排查和数据流程追踪

### 测试建议 ✅
- ✅ 在 `/bookkeeping/review` 页面切换会计科目，检查辅助核算数据是否正确保留
- ✅ 新增辅助核算后保存凭证，检查 API 调用参数中的 `auxiliary` 字段
- ✅ 查看浏览器控制台中的调试日志，确认数据流程正确

## [2025-08-12] - 修复新增科目上级科目选择问题

### 问题修复 🐛
- **上级科目选择限制**：修复 `/bookkeeping/review` 页面新增科目时，上级科目选择器只显示叶子节点的问题
  - **根本原因**：`extractLeafSubjects` 函数过滤了父节点，导致某些科目无法作为上级科目选择
  - **解决方案**：新增 `extractAllSubjects` 函数和 `parentSubjectOptions` 计算属性，专门用于新增科目时选择上级科目
- **科目层级选择**：现在可以选择任何层级的科目作为新增科目的上级科目，包括父节点
- **向后兼容**：保持原有的科目选择逻辑不变，只优化新增科目功能

### 技术优化 ⚡
- **新增 `extractAllSubjects` 函数**：递归提取所有科目节点（包括父节点和叶子节点）
- **新增 `parentSubjectOptions` 计算属性**：基于所有科目生成选项列表，添加 `isParent` 标记
- **更新 `AddSubjectPop.vue` 组件**：使用新的上级科目选项，支持选择父节点
- **扩展 `AccountSubjectOption` 接口**：添加可选的 `isParent` 字段

### 影响范围 📊
- ✅ `/bookkeeping/review` 页面新增科目功能
- ✅ `/voucher/original` 页面新增科目功能
- ✅ 其他使用 `AddSubjectPop` 组件的页面
- ❌ 不影响对方科目选择器（仍使用叶子节点）
- ❌ 不影响现有的科目选择逻辑

### 文件变更 📁
- `M apps/web-antd/src/hooks/jsj-ai/account-book/voucher/index.ts`
- `M apps/web-antd/src/views/jsj-ai/voucher/bookkeeping/enter/AddSubjectPop.vue`

## [2025-08-12] - 修复新增辅助项目保存时数据传递问题

### 问题修复 🐛
- **新增辅助项目保存失败**：修复银行回单编辑中新增辅助项目点击确认后，数据没有正确传递到 `vouchers/update_detail_by_id` 接口的问题
  - **根本原因**：新增辅助项目识别逻辑不完善，保存时无法正确识别和处理新增辅助项目数据
  - **解决方案**：优化辅助项目识别逻辑，增加多重检查机制和本地数据优先查找
- **辅助核算数据丢失**：修复新增辅助项目信息在保存过程中丢失的问题
- **数据识别优化**：完善新增辅助项目的识别逻辑，支持通过 `isNewAuxiliary` 标识和辅助项目代码双重判断

### 技术优化 ⚡
- **多重检查机制**：先检查 `auxiliaryProject === '999999'`，再检查本地数据中的 `isNewAuxiliary` 标识
- **本地数据优先**：优先从本地辅助核算数据中查找新增辅助项目信息，确保数据完整性
- **调试信息增强**：添加详细的辅助项目识别和保存日志，便于问题排查和验证
- **统一处理流程**：新增辅助项目和现有辅助项目使用统一的数据处理流程

### 影响范围 📊
- 银行回单编辑模态框的新增辅助项目保存功能
- 辅助项目的数据识别和传递逻辑
- `vouchers/update_detail_by_id` 接口的辅助核算参数准确性
- 新增辅助项目的完整数据流转过程

### 测试建议 ✅
- 验证新增辅助项目保存后接口调用参数正确
- 确认新增辅助项目的 `id` 为 `null`，`code`、`name`、`type` 字段正确
- 测试新增科目配合新增辅助项目的复合场景
- 验证不同类型辅助项目的保存功能
- 确认不影响现有辅助项目的正常保存

## [2025-08-12] - 修复新增科目保存时数据传递问题

### 问题修复 🐛
- **新增科目保存失败**：修复银行回单编辑中新增科目点击确认后，数据没有正确传递到 `vouchers/update_detail_by_id` 接口的问题
  - **根本原因**：新增科目识别逻辑不完善，保存时无法正确识别和处理新增科目数据
  - **解决方案**：优化 `selectedAccountInfo` 计算属性的识别逻辑，增加多重检查机制和本地数据优先查找
- **数据识别优化**：完善新增科目的识别逻辑，支持通过 `isNewSubject` 标识和科目代码双重判断
- **保存逻辑修复**：确保新增科目保存时使用正确的科目信息，`account_id` 设置为 `null`

### 技术优化 ⚡
- **多重检查机制**：先检查 `counterpartAccount === '404'`，再检查本地数据中的 `isNewSubject` 标识
- **本地数据优先**：优先从本地科目数据中查找新增科目信息，确保数据完整性
- **调试信息增强**：添加详细的保存前检查日志，便于问题排查和验证

### 影响范围 📊
- 银行回单编辑模态框的新增科目保存功能
- 对方会计科目的数据识别和传递逻辑
- `vouchers/update_detail_by_id` 接口的调用参数准确性
- 新增科目的完整数据流转过程

### 测试建议 ✅
- 验证新增科目保存后接口调用参数正确
- 确认新增科目的 `account_id` 为 `null`，`account_code` 和 `account` 字段正确
- 测试连续新增多个科目的保存功能
- 验证不影响现有科目的正常保存

## [2025-08-12] - 优化新增科目和辅助项目的数据源构造逻辑

### 新功能 ✨
- **新增科目数据源构造**：在银行回单编辑时，自动将新增科目（account_code: "404"）添加到本地科目数据源，确保选择器能正确匹配
- **新增辅助项目数据源构造**：自动将新增辅助项目（code: "999999"）添加到本地辅助核算数据源，提升数据一致性
- **智能辅助核算启用**：新增科目默认开启辅助核算，并支持选择所有类型的辅助项目，不受原有科目类型限制

### 问题修复 🐛
- **选择器匹配问题**：修复新增科目和辅助项目在选择器中无法正确匹配和显示的问题
- **辅助核算状态**：修复新增科目的辅助核算启用状态判断逻辑
- **类型安全问题**：使用类型断言解决新增科目属性扩展的 TypeScript 类型错误

### 技术优化 ⚡
- **数据流转优化**：使用 `addLocalSubject()` 和 `addLocalAuxiliary()` 方法实时更新本地数据源
- **类型完整性**：为新增科目和辅助项目补充完整的类型属性，确保数据结构一致性
- **识别逻辑统一**：统一使用明确的标识符（"404"、"999999"）进行新增数据识别

### 影响范围 📊
- `/voucher/original` 页面的银行回单编辑功能
- 对方会计科目选择器的数据匹配逻辑
- 辅助项目选择器的启用状态和选项显示
- 新增科目和辅助项目的数据保存流程

### 测试建议 ✅
- 验证新增科目（account_code: "404"）能正确识别和显示
- 验证新增科目的辅助核算默认开启且可选择所有类型
- 验证新增辅助项目（code: "999999"）能正确识别和显示
- 确认数据保存时正确传递新增科目和辅助项目信息

## [2025-08-12] - 优化银行回单新增科目和辅助项目判断逻辑

### 功能优化 ✨
- **优化新增科目判断逻辑**：修改 `/voucher/original` 页面的新增科目判断条件，统一使用 `account_code` 是否为 "404" 来判断新增科目
  - **修改前**：通过 `useAssistant === undefined && assistantType === undefined` 来判断新增科目
  - **修改后**：直接通过 `account_code === '404'` 来判断新增科目，逻辑更加明确和可靠
- **优化新增辅助项目判断逻辑**：统一使用辅助核算的 `code` 是否为 "999999" 来判断新增辅助项目
  - **修改前**：通过ID匹配失败后再尝试code匹配的方式判断
  - **修改后**：直接通过 `code === '999999'` 来判断新增辅助项目
- **直接使用银行回单数据**：如果是新增科目或新增辅助项目，直接从银行回单列表数据中获取科目和辅助核算信息，不再去科目数据中遍历查找
  - 提高了数据获取的准确性和效率
  - 避免了因科目数据中没有新增科目而导致的查找失败问题

### 技术优化 ⚡
- **统一判断标准**：在 `BankReceiptEditModal.vue` 和 `AuxiliaryProjectSelector.vue` 中统一使用相同的判断条件
- **优化数据流转**：新增科目和辅助项目直接使用银行回单原始数据，减少不必要的数据转换和查找
- **增强代码可维护性**：使用明确的标识符（"404"、"999999"）替代复杂的属性判断逻辑
- **保持向后兼容**：保留了原有的兼容性判断逻辑，确保不影响现有功能

### 影响范围 📊
- `/voucher/original` 页面的银行回单编辑功能
- 新增科目和新增辅助项目的识别和处理逻辑
- 对方会计科目和辅助项目的数据初始化和保存流程
- 提升了新增科目和辅助项目的处理准确性和用户体验

### 测试建议 ✅
- 验证 `account_code` 为 "404" 的记录能正确识别为新增科目
- 验证辅助核算 `code` 为 "999999" 的记录能正确识别为新增辅助项目
- 确认新增科目和辅助项目直接使用银行回单数据，不依赖科目数据查找
- 测试编辑和保存功能的数据传递正确性

## [2025-08-12] - 修复银行回单辅助项目初始化显示问题

### 问题修复 🐛
- **修复银行回单编辑弹框辅助项目显示"无辅助项目"问题**：解决在 `/voucher/original` 页面银行回单中，点击对方会计科目选项打开编辑弹框时，辅助项目显示"无辅助项目"而实际有值的问题
  - **根本原因分析**：
    1. 在 `AuxiliaryProjectSelector` 组件初始化过程中，`selectedAccountId` 的变化会触发监听器清空辅助项目值
    2. 初始化时序问题：表单数据设置了辅助项目值，但在 `auxiliaryOptions` 计算属性完全计算出来之前，监听器就被触发清空了值
    3. 缺乏初始化阶段的保护机制，导致有效的初始值被意外清空
  - **修复 AuxiliaryProjectSelector 组件**：
    - 使用防抖机制处理科目变化：延迟50ms执行清空操作，给 `modelValue` 更新留出时间
    - 智能判断是否清空：检查延迟执行时 `modelValue` 是否有值，有值则保留（编辑现有数据），无值则清空
    - 增强 `auxiliaryOptions` 计算属性：当前值不在选项中时，自动从原始辅助核算数据中查找匹配项并添加为选项
    - 解决连续编辑多个银行回单时的时序冲突问题

### 技术优化 ⚡
- **防抖机制**：通过50ms延迟执行，解决 `selectedAccountId` 和 `modelValue` 变化时序不同步的问题
- **增强数据匹配逻辑**：支持通过ID和code两种方式匹配辅助项目，提高数据查找的准确性
- **优化选项列表构建**：当前值不在选项中时，智能从全局辅助核算数据中查找并添加，确保显示完整性
- **智能清空策略**：根据实际数据状态判断是否需要清空，避免误操作

### 影响范围 📊
- 银行回单编辑功能中的辅助项目显示和选择逻辑
- 对方会计科目和辅助项目的数据初始化流程
- 提升了用户在编辑银行回单时看到正确辅助项目信息的体验

### 测试建议 ✅
- ✅ 打开包含辅助项目的银行回单编辑弹框，验证辅助项目正确显示
- ✅ 检查初始化过程中辅助项目值不会被意外清空
- ✅ 验证科目变化时辅助项目的正确清空和重新加载
- ✅ 测试各种数据状态下的辅助项目显示稳定性

## [2025-08-11] - 修复辅助核算数据传递问题

### 问题修复 🐛
- **修复 review 页面保存时辅助核算数据丢失问题**：解决在 `/bookkeeping/review` 页面点击保存时，辅助核算 `auxiliary` 没有在 `vouchers/update` 接口中带入的问题
  - **根本原因分析**：
    1. `setVoucherDataToEditing` 函数在转换数据格式时，只是简单地将 `item.subjectName` 设置为 `subject.text`
    2. `item.subjectName` 不包含完整的辅助核算信息，导致辅助核算数据在数据传递过程中丢失
    3. VoucherEditing 组件无法获取到包含辅助核算信息的科目文本，影响后续的数据解析和保存
  - **修复 setVoucherDataToEditing 函数**：新增 `formatSubjectWithAuxiliaryForEditing` 函数，从原始 API 数据中提取完整的辅助核算信息
  - **优化科目文本构建**：确保科目文本包含 "科目代码 科目名称 辅助核算代码 辅助核算名称" 的完整格式
  - **增强数据追踪**：在关键位置添加详细的调试日志，便于追踪辅助核算数据的传递过程
  - **保持向后兼容**：修复保持了现有功能的向后兼容性，不影响其他功能

### 技术优化 ⚡
- **数据流程优化**：完善了从 review 页面到 VoucherEditing 组件再到 API 调用的完整数据传递链路
- **调试能力增强**：在 `parseSubjectString`、`handleSaveReview` 等关键函数中添加详细日志
- **代码可维护性提升**：将辅助核算格式化逻辑抽取为独立函数，提高代码复用性

### 影响范围 📊
- 确保了凭证审核保存时辅助核算数据的完整性
- 提升了数据传递的可靠性和准确性
- 改善了开发调试体验，便于问题排查

### 测试建议 ✅
- ✅ 打开包含辅助核算的凭证进行审核
- ✅ 检查 VoucherEditing 组件中科目文本是否包含辅助核算信息
- ✅ 点击保存，检查浏览器控制台中的调试日志
- ✅ 验证 `vouchers/update` API 调用参数中的 `auxiliary` 字段是否正确传递

## [2025-08-11] - 新增辅助项目验证功能

### 新功能 ✨
- **辅助项目新增验证功能**：在 `/bookkeeping/review` 页面新增辅助项目时，根据当前科目的 `useAssistant` 和 `assistantType` 进行验证
  - **科目辅助核算状态验证**：检查科目是否开启了辅助核算功能，未开启则阻止新增并提示用户
  - **辅助核算类型匹配验证**：验证要新增的辅助项目类型是否与科目配置的辅助核算类型匹配
  - **新增科目特殊处理**：新增科目默认开启辅助核算，支持选择所有辅助核算类型，不受限制
  - **智能科目识别**：通过 `isNewSubject` 标记或 `p_account_code` 字段自动识别新增科目
  - **智能类型映射**：支持 API 类型（s-供应商, c-客户, i-存货, p-项目, d-部门, e-员工）到组件类型的自动映射
  - **用户友好提示**：为不同验证失败情况提供清晰的错误提示信息，指导用户正确操作

### 技术优化 ⚡
- **AddAuxiliaryPop 组件增强**：在 `open` 方法中添加完整的验证逻辑，确保数据一致性
- **VoucherEditing 组件优化**：改进 `addAuxiliaryClick` 方法，增强科目信息获取和验证逻辑
- **类型安全处理**：完善辅助核算类型映射和验证机制，避免类型不匹配问题
- **单元测试覆盖**：新增 6 个测试用例，覆盖各种验证场景，确保功能稳定性

### 影响范围 📊
- 提升了凭证录入和审核阶段的数据准确性
- 防止了不符合科目配置的辅助项目被错误添加
- 改善了用户操作体验，减少了数据录入错误
- 增强了系统的数据完整性和一致性

### 测试覆盖 ✅
- ✅ 科目未开启辅助核算的验证
- ✅ 科目未配置辅助核算类型的验证
- ✅ 辅助核算类型不匹配的验证
- ✅ 正确配置科目的正常流程
- ✅ 缺少科目信息的异常处理
- ✅ API 类型到组件类型的映射验证
- ✅ 新增科目支持所有类型选择的验证
- ✅ 通过 p_account_code 识别新增科目的验证

## [2025-08-11] - 修复 review 页面新增科目后无法添加辅助核算的问题

### 问题修复 🐛
- **修复新增科目辅助核算添加失败问题**：解决在 `/bookkeeping/review` 页面中新增科目后无法为该科目添加辅助核算项目的问题
  - **根本原因分析**：
    1. `parseSubjectString` 函数处理新增科目时强制清空 `auxiliaryInfo` 为空数组
    2. 新增科目的 `useAssistant` 和 `assistantType` 设置为 `undefined`，导致辅助核算功能无法正常启用
    3. 新增科目识别逻辑不够完善，无法正确识别所有新增科目场景
  - **修复 parseSubjectString 函数**：保留原始辅助核算信息，添加新增科目标记和默认启用辅助核算
  - **优化新增科目数据结构**：设置 `useAssistant: true` 和 `assistantType: 'customer'` 作为默认值
  - **增强新增科目识别逻辑**：在 `AuxiliaryProjectSelector` 组件中增加多种识别方式
  - **修复科目显示文本函数**：修复 `getSubjectDisplayText` 函数逻辑，确保新增辅助核算后能正确显示完整的科目文本
  - **修复数据验证问题**：修复新增辅助核算后科目对象缺少 `assistantOptions` 字段导致的验证失败问题

### 技术优化 ⚡
- **数据一致性增强**：确保新增科目的辅助核算相关属性正确设置和传递
- **组件间通信优化**：改进科目对象在不同组件间的数据流转逻辑
- **类型安全处理**：使用类型断言处理扩展属性，避免 TypeScript 类型错误
- **调试支持增强**：添加详细的控制台日志，便于问题排查和功能验证

### 影响范围 📊
- `/bookkeeping/review` 页面的新增科目功能
- 新增科目的辅助核算添加和管理功能
- 科目数据结构的向后兼容性保持
- 提升了用户在凭证审核阶段的操作体验

### 测试建议 ✅
- 在 review 页面新增科目后验证辅助核算功能
- 确认新增科目能正常保存和显示辅助核算项目
- 验证不影响现有科目的辅助核算功能

## [2025-08-12] - 最近一周功能更新汇总（8月5日-8月12日）

### 🎯 核心功能更新

#### 银行回单编辑功能完善
- **新增科目和辅助项目数据源构造**：实现新增科目和辅助项目的智能识别和数据源构造
- **在线编辑功能**：完善银行回单对方科目和辅助项目的在线编辑功能
- **辅助项目显示优化**：修复辅助项目初始化显示问题，提升用户体验

#### 凭证处理功能增强
- **凭证导航功能**：新增上一张/下一张凭证导航功能，提升凭证编辑效率
- **凭证打印优化**：直接使用接口返回的完整URL，简化打印流程
- **部分数据生成凭证**：支持勾选部分数据生成凭证功能，增加操作灵活性

#### 科目映射功能优化
- **映射参数优化**：增加 `cfg_id` 和 `cfg_code` 字段支持，完善科目映射逻辑
- **选择逻辑优化**：实现科目选择逻辑优化和分页修复
- **配置功能完善**：新增科目映射配置功能

### 🔧 技术架构优化

#### 语音命令系统重构
- **ActionHandlers统一管理**：重构语音命令处理机制，使用ActionHandlers统一管理
- **命令处理优化**：提升语音命令的响应速度和准确性

#### 发票处理功能增强
- **AI记账优化**：优化进项发票AI记账功能，支持按发票类型自动拆分处理
- **知识库同步**：实现发票场景更新时自动同步到知识库功能

#### 界面交互优化
- **公司选择框优化**：实现顶部栏公司选择框复制按钮，内嵌显示，悬浮可见
- **表格显示优化**：优化原始凭证页面表格显示和金额统计功能

### 📊 问题修复汇总
- 修复 review 页面保存时辅助核算数据传递问题
- 修复新增科目名称不更新的问题
- 修复废弃引用相关问题
- 修复银行回单编辑弹框辅助项目显示问题

### 🎉 整体提升
- **用户体验**：银行回单编辑流程更加流畅，新增科目处理更加智能
- **功能完整性**：凭证处理、科目映射、发票处理等核心功能得到全面增强
- **系统稳定性**：修复多个关键问题，提升系统整体稳定性
- **开发效率**：优化代码结构，提升开发和维护效率

## [2025-08-11] - 最近一周功能更新汇总

### 新功能 ✨
- **银行回单在线编辑功能**：实现银行回单对方科目和辅助项目的在线编辑功能
  - 新增 `BankReceiptEditModal`、`CounterpartAccountSelector`、`AuxiliaryProjectSelector` 组件
  - 支持在银行回单列表中直接编辑对方科目和辅助项目
  - 添加 `updateVoucherDetailById` API 接口支持凭证明细更新
- **科目映射参数优化**：优化科目映射参数获取逻辑，增加 `cfg_id` 和 `cfg_code` 字段支持
- **发票场景知识库同步**：实现发票场景更新时自动同步到知识库功能

### 问题修复 🐛
- **废弃引用修复**：修复代码中的废弃引用问题，提升代码质量
- **新增科目显示问题**：修复新增科目和辅助项目显示不正确的问题
- **数据初始化优化**：改进组件数据初始化逻辑，处理潜在的空值问题

### 技术改进 ⚡
- **API 接口扩展**：新增多个 API 接口支持新功能开发
- **组件复用性增强**：提取通用组件，提高代码复用性
- **数据处理优化**：改进数据处理逻辑，确保数据一致性和准确性

## [2025-08-11] - 修复新增科目名称不更新的问题

### 问题修复 🐛
- **修复新增科目名称显示错误问题**：解决用户新增科目（如 888888）后，系统仍然显示旧科目名称（如"反反复复反反复复"）的问题
  - **根本原因**：`addLocalSubject` 方法只是简单地将新科目 `push` 到数组末尾，当存在相同 code 的科目时，`find` 方法总是返回第一个匹配的（旧的）科目
  - **修复 addLocalSubject 方法**：在添加新科目前检查是否已存在相同 code 的科目，如果存在则替换现有科目而不是追加新科目
  - **添加重复检查逻辑**：使用 `findIndex` 方法查找现有科目，存在时替换，不存在时添加
  - **增强调试支持**：添加详细的日志输出，便于调试和追踪科目替换过程

### 技术优化 ⚡
- **智能科目管理**：新增科目时自动检测并替换同代码的旧科目，确保数据一致性
- **缓存同步更新**：修改科目数据后自动更新本地缓存，保持数据同步
- **类型安全增强**：添加空值检查，避免 TypeScript 警告和潜在的运行时错误

### 影响范围 📊
- 影响所有使用 `addLocalSubject` 方法的组件
- 主要影响凭证编辑和银行回单编辑功能
- 确保新增科目能正确替换同代码的旧科目，显示正确的科目名称

### 测试验证 ✅
- 创建测试用例验证替换逻辑正确性
- 确保新增科目后能正确显示新的科目名称
- 验证缓存更新机制正常工作

## [2025-08-11] - 修复新增科目后 vouchers/update_detail_by_id 接口 account 字段未更新问题

### 问题修复 🐛
- **修复新增科目后接口调用失败问题**：解决编辑对方会计科目和辅助项目时，点击新增科目后 `vouchers/update_detail_by_id` 接口中的 account 内容有时没有修改的问题
  - **根本原因**：新增科目的数据结构问题导致 `extractLeafSubjects` 函数无法正确识别新增科目，`selectedAccountInfo` 计算属性返回 `null`
  - **修复 extractLeafSubjects 函数**：增强叶子节点提取逻辑，额外处理标记为 `isNewSubject` 的新增科目
  - **改进 addLocalSubject 函数**：确保新增科目有正确的 `isNewSubject` 标记和 `children: []` 结构
  - **增强 selectedAccountInfo 计算属性**：增加详细的调试信息，支持通过 ID 和 code 进行科目匹配
  - **完善 handleSave 函数**：增加 `selectedAccount` 为 `null` 时的错误处理和用户提示
  - **优化新增科目数据结构**：确保新增科目包含所有必要字段和正确的标记

### 技术优化 ⚡
- **智能科目识别**：新增科目使用 `code` 匹配，现有科目使用 `id` 匹配，确保兼容性
- **全面调试支持**：增加详细的控制台日志，便于问题排查和调试
- **错误处理增强**：防止无效数据提交到后端，提供明确的用户反馈
- **数据结构规范**：统一新增科目的数据格式，确保与现有系统兼容

### 影响范围 📊
- 银行回单编辑功能中的新增科目处理逻辑
- 对方会计科目选择和保存功能
- 提升了新增科目后的数据一致性和用户体验

### 最近一周更新汇总 📅

#### [2025-08-11]
- **修复新增科目名称不更新问题**：解决新增科目后仍显示旧科目名称的问题，优化 addLocalSubject 方法
- **修复废弃引用**：清理代码中的废弃引用
- **银行回单编辑功能**：实现银行回单对方科目和辅助项目的在线编辑

#### [2025-08-09]
- **代码合并**：解决主分支冲突并合并

#### [2025-08-08]
- **科目映射优化**：优化科目映射参数获取逻辑
- **构建配置**：修改打包配置
- **进项发票AI记账**：支持按发票类型自动拆分处理

#### [2025-08-07]
- **凭证导航功能**：凭证编辑页面新增上一张/下一张凭证导航功能
- **凭证打印优化**：直接使用接口返回的完整URL
- **原始凭证页面**：优化表格显示和金额统计功能
- **公司选择框**：实现顶部栏公司选择框复制按钮，内嵌显示，悬浮可见
- **科目映射功能**：实现科目选择逻辑优化和分页修复

#### [2025-08-06]
- **语音命令重构**：使用ActionHandlers统一管理语音命令处理机制
- **科目映射配置**：新增科目映射配置功能
- **项目文档**：删除MIT许可，修改README，提交changelog

#### [2025-08-05]
- **部分数据生成凭证**：支持勾选部分数据生成凭证功能
- **SSE接口优化**：注释未使用的SSE关闭接口

## [2025-08-11] - 修复 BankReceiptEditModal 空指针异常

### 问题修复 🐛
- **修复银行回单编辑模态框空指针异常**：解决 BankReceiptEditModal.vue 第181行发生的 `TypeError: Cannot read properties of null (reading 'toString')` 错误
  - 错误原因：`matchedSubject.id` 可能为 `null` 或 `undefined`，但代码直接调用了 `toString()` 方法
  - 修复方案：在第180-181行添加了额外的空值检查：`matchedSubject && matchedSubject.id`
  - 确保只有在 `matchedSubject` 存在且 `matchedSubject.id` 不为空时才调用 `toString()` 方法

### 技术优化 ⚡
- 提高了代码的健壮性，避免了空指针异常
- 改进了银行回单编辑模态框的表单数据初始化逻辑
- 增强了错误处理机制，确保组件在各种数据状态下都能正常工作

### 影响范围 📊
- 银行回单编辑模态框的表单数据初始化逻辑
- 对方会计科目选择和显示功能
- 提升了用户在编辑银行回单时的体验稳定性

## [2025-08-04 至 2025-08-11] - 最近一周更新汇总

### 📅 2025-08-09
- **代码合并**：Merge branch 'main' with conflict resolution

### 📅 2025-08-08
- **科目映射优化**：优化科目映射参数获取逻辑
- **构建配置**：打包配置修改
- **进项发票AI记账**：优化进项发票AI记账功能，支持按发票类型自动拆分处理

### 📅 2025-08-07
- **凭证导航功能**：凭证编辑页面新增上一张/下一张凭证导航功能
- **凭证打印优化**：优化凭证打印功能，直接使用接口返回的完整URL
- **原始凭证页面**：优化原始凭证页面表格显示和金额统计功能
- **公司选择框**：实现顶部栏公司选择框复制按钮，内嵌显示，悬浮可见
- **科目映射功能**：优化科目映射功能，实现科目选择逻辑优化和分页修复

### 📅 2025-08-06
- **语音命令重构**：重构语音命令处理机制，使用ActionHandlers统一管理
- **科目映射配置**：新增科目映射配置功能
- **项目文档**：删除MIT许可，修改README文档
- **更新日志**：提交changelog更新

### 📅 2025-08-05
- **数据生成凭证**：支持勾选部分数据生成凭证功能
- **SSE接口优化**：注释未使用的SSE关闭接口

### 📅 2025-08-04
- **分支合并**：Merge branch 'hz-zzx' into pre



## [2025-08-11] - 修复对方会计科目和辅助项目匹配不到时的显示问题

### 问题修复 🐛
- **修复新增科目和辅助项目显示空白问题**：解决编辑对方会计科目和辅助项目时，如果新增的科目或辅助项目在数据源中匹配不到，显示为空的问题
  - 修复 AuxiliaryProjectSelector.vue 组件：当前值在选项中不存在时，自动添加为默认选项并直接显示原始内容
  - 修复 CounterpartAccountSelector.vue 组件：当前科目值在选项中不存在时，自动添加为默认选项并直接显示原始内容
  - 优化 BankReceiptEditModal.vue 中的表单初始化逻辑：当匹配不到科目或辅助项目时，使用原始数据作为默认值
  - **修复新增科目后显示问题**：修复新增科目后只显示account_code的问题，现在会同时显示account_code和account名称
  - **修复新增辅助项目后显示问题**：修复新增辅助项目后的显示格式，确保code和name都能正确显示
  - 增强数据容错性：支持通过ID、code或完整信息进行匹配，确保各种情况下都能正确显示

### 技术优化 ⚡
- **增强选项列表处理逻辑**：
  - AuxiliaryProjectSelector：检查当前值是否在选项中，不存在则添加为默认选项
  - CounterpartAccountSelector：使用 enhancedSubjectOptions 计算属性动态处理选项列表
  - 统一处理新增项目和现有项目的显示逻辑，确保用户界面一致性
- **优化表单初始化**：
  - 改进科目匹配逻辑：优先使用ID匹配，失败时使用code匹配，最后使用完整信息
  - 改进辅助项目设置：支持通过ID、code或name进行设置，增强兼容性
  - **增强显示逻辑**：当有code和name时，优先组合显示"code name"格式，提供更完整的信息
  - **智能解析功能**：支持解析"code name"格式的字符串，自动拆分为code和name部分
  - **优化新增项目事件处理**：修复新增科目和辅助项目的事件监听逻辑，确保正确组合显示code和name
  - 添加详细的日志输出，便于调试和问题排查

### 用户体验 🎯
- 新增的科目或辅助项目现在能正确显示原始内容，而不是显示为空白
- 用户可以看到完整的科目和辅助项目信息，即使这些项目是新增的且尚未同步到数据源
- 提升了编辑对方会计科目和辅助项目时的用户体验和数据可见性
- **优化显示格式**：默认选项现在同时显示code和name，格式为"代码 名称"，提供更完整的信息展示

## [2025-08-11] - 修复新增科目接口调用问题

### 问题修复 🐛
- **修复新增科目后接口调用失败问题**：解决编辑对方会计科目和辅助项目时，新增科目后调用 `vouchers/update_detail_by_id` 接口无法正确传入新增科目信息的问题
  - 修复 BankReceiptEditModal.vue 中 `selectedAccountInfo` 计算属性的科目查找逻辑，支持通过ID和code进行匹配
  - 修复辅助项目查找逻辑，支持通过ID和code进行匹配，确保新增的辅助项目也能正确匹配
  - 修改新增科目和辅助项目的ID生成策略：不再由前端生成ID，设置为null由后端生成
  - 使用科目code和辅助项目code作为临时标识符，用于前端匹配和选择
  - **修复null值toString()错误**：修复新增科目和辅助项目ID为null时调用toString()方法导致的运行时错误
  - 添加详细的调试日志，便于跟踪接口调用参数和科目匹配过程

### 技术优化 ⚡
- **优化ID匹配策略**：优先通过ID匹配现有项目，ID匹配失败时通过code匹配新增项目
- **规范ID生成机制**：新增科目和辅助项目的ID设置为null，由后端统一生成和管理
- **增强兼容性**：同时支持现有项目（数字ID）和新增项目（code标识）的匹配逻辑
- **完善数据传递**：确保新增科目的 `account_id`、`account_code`、`account` 字段和辅助项目的相关字段正确传递给后端接口
- **添加标识字段**：为新增项目添加 `isNewSubject` 和 `isNewAuxiliary` 标识，便于区分新增和现有项目
- **修复空值处理**：在所有调用toString()的地方添加null值检查，使用三元运算符确保安全性
  - hooks/index.ts: 修复科目选项生成时的ID处理
  - AuxiliaryProjectSelector.vue: 修复辅助项目选项生成时的ID处理
  - BankReceiptEditModal.vue: 修复表单初始化时的科目ID处理

### 最近一周更新汇总 📅

#### 2025-08-09
- Merge branch 'main' with conflict resolution

#### 2025-08-08
- feat: 优化科目映射参数获取逻辑
- chore: 打包配置修改
- feat: 优化进项发票AI记账功能 - 支持按发票类型自动拆分处理

#### 2025-08-07
- feat: 凭证编辑页面新增上一张/下一张凭证导航功能
- feat: 优化凭证打印功能 - 直接使用接口返回的完整URL
- feat: 优化原始凭证页面表格显示和金额统计功能
- feat: 实现顶部栏公司选择框复制按钮 - 内嵌显示，悬浮可见
- feat: 优化科目映射功能 - 实现科目选择逻辑优化和分页修复

#### 2025-08-06
- feat: 重构语音命令处理机制，使用ActionHandlers统一管理
- feat: 科目映射配置
- feat: 删除mit许可
- feat: readme修改
- feat: changelog提交

#### 2025-08-05
- feat: 支持勾选部分数据生成凭证功能
- feat: 注释未使用的SSE关闭接口

## [2025-08-11] - 辅助项目类别固定功能

### 新功能 ✨
- **辅助项目类别根据科目类型固定**：优化新增辅助项目功能，辅助类别根据当前科目的 assistantType 固定死类型，无法选择
  - 修改 AddAuxiliaryPop.vue 组件，新增计算属性判断是否应该禁用辅助类别选择器
  - 当有科目信息且科目有辅助核算类型时，辅助类别自动根据科目类型确定
  - 辅助类别选择器在固定时显示为只读输入框，并显示明确的提示信息
  - 保持原有的类型映射逻辑，支持 API 类型到组件类型的转换

### 用户体验 🎯
- 用户在新增辅助项目时，系统自动根据当前科目的辅助核算类型确定辅助类别
- 显示明确的提示信息："辅助类别已根据当前科目的辅助核算类型自动确定，无法修改"
- 确保辅助项目类型与科目类型的一致性，避免用户选择错误的类型

### 技术优化 ⚡
- 新增 `isTypeDisabled` 计算属性，智能判断是否应该禁用辅助类别选择器
- 新增 `currentTypeLabel` 计算属性，显示当前辅助类别的中文标签
- 优化 `open` 方法，增强科目信息处理逻辑，确保正确设置辅助类别类型
- 条件渲染不同的输入组件：固定时显示只读输入框，否则显示选择器

## [2025-08-11] - 新增科目辅助核算默认启用功能

### 新功能 ✨
- **新增科目辅助核算默认启用**：优化对方会计科目新增功能，新增科目后辅助核算默认可以启用
  - 修改 AddSubjectPop.vue 组件，新增科目时默认启用辅助核算（useAssistant: true）
  - 添加辅助核算类型选择功能，支持客户、供应商、员工、部门、项目、存货等所有类型
  - 新增表单项：启用辅助核算开关和辅助核算类型选择器
  - 优化新增科目对象构建逻辑，正确设置 useAssistant 和 assistantType 字段

### 技术优化 ⚡
- **AuxiliaryProjectSelector 组件优化**：修复新增科目的辅助核算识别问题
  - 优化科目查找逻辑，支持数字ID和字符串ID的兼容性（新增科目使用字符串ID）
  - 确保新增科目的辅助核算能正常工作和显示
- **辅助核算类型映射完善**：确保所有6种辅助核算类型都能正确支持
  - 客户(customer)、供应商(supplier)、员工(employee)、部门(department)、项目(project)、存货(inventory)

### 用户体验 🎯
- 新增科目时用户可以直接配置辅助核算，无需后续手动设置
- 辅助核算类型选择器提供友好的中文标签显示
- 支持动态显示/隐藏辅助核算类型选择，根据启用状态自动调整

## [2025-08-11] - 辅助项目显示修复

### 修复 🐛
- **修复辅助项目默认值显示问题**：修复 /voucher/original 页面编辑对方会计科目和辅助项目时，辅助项目默认值显示 ID 而非 label 的问题
  - 修复科目变化时的清空逻辑，避免初始化时误清空辅助项目选择
  - 修复初始值设置，保持原始值不被转换为 undefined
  - 添加立即监听确保 props.modelValue 的初始值正确设置
  - 修复类型一致性，将辅助项目选项的 value 统一为字符串类型
  - 现在辅助项目正确显示 "代码 名称" 格式的 label，而不是显示 ID

### 技术优化 ⚡
- 优化 AuxiliaryProjectSelector 组件的数据绑定和监听逻辑
- 改进组件初始化时序，确保数据正确加载和显示
- 统一数据类型处理，避免字符串和数字类型不匹配问题

### 最近一周更新总结 📅

#### [2025-08-11]
- **辅助项目类别固定功能**：新增辅助项目中辅助类别根据科目类型固定，无法选择
- **新增科目辅助核算默认启用**：优化对方会计科目新增功能，新增科目后辅助核算默认可以启用
- **辅助项目显示修复**：修复辅助项目默认值显示问题

#### [2025-08-09]
- **代码合并**：解决主分支冲突并合并

#### [2025-08-08]
- **科目映射优化**：优化科目映射参数获取逻辑
- **构建配置**：修改打包配置
- **进项发票AI记账**：支持按发票类型自动拆分处理

#### [2025-08-07]
- **凭证导航功能**：凭证编辑页面新增上一张/下一张凭证导航功能
- **凭证打印优化**：直接使用接口返回的完整URL
- **原始凭证页面**：优化表格显示和金额统计功能
- **公司选择框**：实现顶部栏公司选择框复制按钮，内嵌显示，悬浮可见
- **科目映射功能**：实现科目选择逻辑优化和分页修复

#### [2025-08-06]
- **语音命令重构**：使用ActionHandlers统一管理语音命令处理机制
- **科目映射配置**：新增科目映射配置功能
- **项目文档**：删除MIT许可，修改README，提交changelog

#### [2025-08-05]
- **部分数据生成凭证**：支持勾选部分数据生成凭证功能
- **SSE接口优化**：注释未使用的SSE关闭接口

#### [2025-08-05]
- **凭证编辑优化**：会计科目切换后保存时正确拆解科目和辅助核算信息
- **发票场景接口**：为发票场景更新接口增加detail_id参数支持

## [2025-08-10] - 修复循环引用错误和最近一周功能总结

### 修复 🐛
- **修复Vue组件循环引用错误**：
  - 修复 CounterpartAccountSelector 和 AuxiliaryProjectSelector 组件中 menuNode 的循环引用问题
  - 将 `{{ menuNode }}` 改为 `<component :is="menuNode" />` 避免JSON序列化错误
  - 清理未使用的 computed 导入

### 最近一周更新总结 📅

#### [2025-08-09]
- **代码合并**：解决主分支冲突并合并

#### [2025-08-08]
- **科目映射优化**：优化科目映射参数获取逻辑
- **构建配置**：修改打包配置
- **进项发票AI记账**：支持按发票类型自动拆分处理

#### [2025-08-07]
- **凭证导航功能**：凭证编辑页面新增上一张/下一张凭证导航功能
- **凭证打印优化**：直接使用接口返回的完整URL
- **原始凭证页面**：优化表格显示和金额统计功能
- **公司选择框**：实现顶部栏公司选择框复制按钮，内嵌显示，悬浮可见
- **科目映射功能**：实现科目选择逻辑优化和分页修复

#### [2025-08-06]
- **语音命令重构**：使用ActionHandlers统一管理语音命令处理机制
- **科目映射配置**：新增科目映射配置功能
- **项目文档**：删除MIT许可，修改README，提交changelog

#### [2025-08-05]
- **部分数据生成凭证**：支持勾选部分数据生成凭证功能
- **SSE接口优化**：注释未使用的SSE关闭接口

#### [2025-08-04]
- **凭证编辑优化**：会计科目切换后保存时正确拆解科目和辅助核算信息
- **发票场景接口**：为发票场景更新接口增加detail_id参数支持

## [2025-08-10] - 银行回单对方会计科目和辅助项目功能

### 新增 ✨
- **银行回单对方会计科目编辑功能**：在 /voucher/original 银行回单页面新增对方会计科目和辅助项目编辑功能
  - 在AI记账场景后新增"对方会计科目"和"辅助项目"两列
  - 支持点击编辑对方会计科目和辅助项目
  - 集成 updateVoucherDetailById 接口实现数据保存
  - 支持新增科目和新增辅助项目功能

- **专用组件开发**：
  - CounterpartAccountSelector：对方会计科目选择组件
  - AuxiliaryProjectSelector：辅助项目选择组件，支持未开启辅助核算的处理
  - BankReceiptEditModal：银行回单编辑模态框

### 改进 🚀
- **数据联动逻辑**：
  - 对方会计科目变化时自动清空并重新加载辅助项目选项
  - 辅助项目选择框根据科目的辅助核算设置自动启用/禁用
  - 未开启辅助核算时显示"未开启辅助核算"并禁用选择框
  - 支持"无辅助项目"选项，值为null

- **LLM输出处理**：
  - 新增辅助项目时自动填充LLM输出的辅助项目值
  - 从银行回单的对方户名称中提取作为默认辅助项目值
  - AddAuxiliaryPop组件支持预填充功能

- **编辑凭证页面联动**：
  - 在凭证审核页面修改凭证后，自动同步更新银行回单的对方会计科目信息
  - 实现双向数据同步，确保数据一致性

### 技术实现 ⚡
- 修改 getBankReceiptColumns 函数支持编辑回调
- 使用 other_side 字段标识对方科目
- 集成现有的会计科目和辅助项目选择器组件
- 实现事件监听机制处理新增科目和辅助项目
- 优化表格列渲染，支持可点击的编辑链接

### 用户体验 🎯
- 默认展示 voucher_detail 中 other_side: true 的科目信息
- 科目编码默认404，辅助核算项目默认99999，不支持修改
- 修改列表内的项目和辅助后对应的凭证联动修改
- 辅助项目取对方会计科目的辅助项目

## [2025-08-10] - 凭证审核页面辅助核算功能增强

### 新增 ✨
- **智能辅助核算新增功能**：在 /bookkeeping/review 凭证审核页面实现基于当前科目的辅助核算新增
  - 新增辅助核算时自动获取当前选中行的科目信息
  - 辅助核算与科目自动关联，确保数据一致性
  - 新增完成后自动更新当前凭证页面显示内容
  - 支持实时页面内容替换，无需手动刷新

### 改进 🚀
- **AddAuxiliaryPop 组件优化**：
  - 支持接收当前科目信息作为参数
  - 在弹框中显示当前科目信息，提升用户体验
  - 新增的辅助核算自动关联到指定科目
  - 优化事件通信机制，传递完整的科目和辅助核算信息

- **VoucherEditing 组件增强**：
  - 优化 addAuxiliaryClick 方法，智能获取当前行科目信息
  - 支持从 voucherStore 中获取科目信息作为备选方案
  - 增强 listenAuxiliaryMitt 事件处理，实现页面实时更新
  - 自动更新当前行的科目显示文本，包含新增的辅助核算

- **Review 页面事件处理**：
  - 添加辅助核算新增事件监听器
  - 实现 localStorage 凭证数据的实时更新
  - 自动触发 VoucherEditing 组件重新渲染
  - 优化科目字符串解析机制

### 用户体验 🎯
- 用户在凭证审核页面新增辅助核算时，系统自动识别当前科目
- 新增完成后，页面立即显示包含辅助核算的完整科目信息
- 无需手动刷新或重新选择，提升操作流畅性
- 弹框中显示当前科目信息，让用户明确知道正在为哪个科目添加辅助核算

### 技术优化 ⚡
- 优化事件通信机制，使用统一的数据格式传递科目和辅助核算信息
- 增强错误处理和边界情况处理
- 改进组件间的数据同步机制
- 优化页面渲染性能，避免不必要的重新渲染

### 修复 🐛
- **辅助核算重复显示问题**：修复新增辅助核算时出现重复显示的问题
  - 优化科目文本解析逻辑，正确提取纯科目部分（科目代码 + 科目名称）
  - 新增辅助核算时替换原有辅助核算，而不是追加
  - 确保科目显示格式统一：科目代码 + 科目名称 + 辅助核算代码 + 辅助核算名称

- **辅助核算选项更新问题**：修复新增辅助核算后选项列表不更新的问题
  - 优化 AddAuxiliaryPop 组件的数据结构，符合 AssistantAccountingItem 接口规范
  - 改进辅助核算类型映射机制，支持API类型到组件类型的转换
  - 确保新增的辅助核算能够正确添加到全局选项列表中

## [2025-08-10] - 新增科目功能优化

### 改进 🚀
- **新增科目数据结构优化**：优化 /bookkeeping/review 页面新增科目功能的数据处理逻辑
  - 上级科目的 code 现在存储到 `p_account_code` 字段中，便于数据追溯和管理
  - 新增科目名称单独存储到 `account` 字段，不再拼接到一级科目后面
  - 科目显示文本优化，新增科目直接显示科目名称，提升可读性
  - 修改 `parseSubjectString` 函数，支持新增科目的特殊处理逻辑

### 用户体验 🎯
- 新增科目在凭证页面中的显示更加清晰，避免了冗长的拼接名称
- 科目层级关系通过 `p_account_code` 字段维护，保持数据完整性
- 凭证编辑时科目选择和显示逻辑更加直观

### 技术优化 ⚡
- 增强 `VoucherEditing.vue` 中的科目选择处理逻辑
- 优化 `AddSubjectPop.vue` 中新增科目的数据构建
- 改进 review 页面的科目解析函数，支持新增科目的特殊格式

## [2025-08-09] - 凭证编辑功能增强

### 新增 ✨
- **新增科目弹框功能**：在 /bookkeeping/review 页面新增科目功能
  - 重新设计简洁的新增科目弹框，去除原有复杂的垃圾代码
  - 科目编码默认设置为 404，符合用户需求
  - 支持选择上级科目，输入科目名称
  - 集成到凭证编辑页面，方便用户快速添加科目

- **新增辅助核算弹框功能**：在 /bookkeeping/review 页面新增辅助核算功能
  - 创建新的辅助核算弹框组件，支持客户、供应商、员工等类型
  - 辅助核算编码默认设置为 999999，符合用户需求
  - 支持选择辅助核算类别（客户、供应商、员工、部门、项目、存货）
  - 添加对应的 API 接口支持数据保存

### 改进 🚀
- **API 接口扩展**：新增 addAuxiliarySave 接口，支持不同类型的辅助核算项目保存
- **页面集成优化**：在凭证编辑页面顶部添加"新增科目"和"新增辅助"按钮，提升用户体验

### 用户体验 🎯
- 用户在编辑凭证时可以直接新增科目和辅助核算，无需跳转到其他页面
- 简化的弹框界面，只包含必要字段，提升操作效率
- 按钮位置合理，与现有功能按钮保持一致的设计风格

## [2025-08-08] - 进项发票处理优化

### 改进 🚀
- **进项发票AI记账功能优化**：优化进项发票勾选后的AI记账处理逻辑
  - 根据发票的 `type` 字段（如"数电票(专票)"和"数电票(普票)"）自动判断发票类型
  - 当选中多种类型的进项发票时，自动按类型拆分并分别调用 `db_to_voucher_request`
  - 进项专票和一般进项分两次单独调用，避免混合处理
  - 新增发票类型智能识别函数，支持专票、普票的自动分类
  - 优化调用时序，进项专票优先处理，一般进项延迟1秒处理，避免并发冲突

### 用户体验 🎯
- 用户勾选不同类型的进项发票后，系统会自动识别并分类处理，无需手动区分
- 提升了混合发票类型的处理准确性和效率

## [2025-08-07] - 最新更新

### 新增 ✨
- **凭证编辑页面导航功能**：在凭证编辑页面新增上一张/下一张凭证导航功能
  - 在页面标题栏显示当前凭证位置信息（如：3 / 10）
  - 添加上一张/下一张导航按钮，支持快速切换凭证
  - 支持键盘快捷键：左箭头键（←）切换到上一张，右箭头键（→）切换到下一张
  - 导航基于当前筛选条件下的凭证列表，确保导航逻辑正确
  - 解决与原始文件导航快捷键冲突问题，凭证导航使用箭头键，原始文件导航使用 Ctrl + 箭头键组合
- 实现顶部栏公司选择框复制按钮功能，内嵌显示，悬浮可见
- 银行回单页面底部添加收入和支出金额合计统计功能，支持实时计算和格式化显示
- 发票页面底部添加合计金额、税额、价税合计统计功能，支持精确计算
- 发票表格新增不含税金额列，显示在价税合计前
- 引入 number-precision 库解决JavaScript浮点数精度问题
- 新增金额精度计算工具函数，确保财务数据计算准确性

### 改进 🚀
- **优化凭证打印功能**：/bookkeeping/view 和 /bookkeeping/review 页面的打印功能现在直接使用 voucher/generate_pdf 接口返回的完整 URL，无需再进行 URL 拼接

### 用户体验 🎯
- 凭证编辑时无需返回列表页面即可快速浏览其他凭证，大幅提升工作效率
- 键盘快捷键支持让用户可以更快速地进行凭证审核工作
- 直观的位置指示器让用户清楚了解当前进度

## [2025-08-06] - 配置优化

### 新增 ✨
- 科目映射配置功能

### 移除 ❌
- 删除MIT许可证

## [2025-08-05] - 功能增强

### 新增 ✨
- 支持勾选部分数据生成凭证功能

### 改进 🚀
- 注释未使用的SSE关闭接口


## [2025-08-04] - 凭证功能优化

### 改进 🚀
- 凭证编辑页面会计科目切换后保存时正确拆解科目和辅助核算信息
- 为发票场景更新接口增加detail_id参数支持


## [2025-08-01] - AI功能增强

### 新增 ✨
- AI聊天页面新增语音录入功能

### 改进 �
- 修复场景分录编辑弹框中分录详情选择框数据源选项问题

### 修复 🐛
- 发送图片url地址修改

## [2025-07-31] - 系统重构

### 改进 🚀
- 重构文件上传功能并更新生产环境配置
- 优化科目选择器，移除场景分录配置中的辅助核算信息显示

## [2025-07-30] - 版本归档

### 系统优化 🚀
- 接口鉴权功能增强
- 凭证列表添加筛选功能和Tooltip提示
- 银行名称筛选增加账号显示
- 批量写入添加二次确认弹窗
- 限制页面最小宽度，优化布局体验
- 税款所属期筛选功能
- 缓存滚动位置，保留页面返回时的滚动位置
- 同步客户列表功能
- 更多筛选功能
- 凭证列表增加筛选功能
- 展开AI对话框后,自动打开"原始票据"的页面

### 数据处理 📊
- 发票和回单原文件改为oss存储
- 增加清除当月公司数据功能
- 科目显示使用fullName替代name字段
- 动态获取请求地址配置
- 增加config配置文件
- 为companies/sync接口添加月份参数支持
- 简化 users/get_user_info 接口返回参数，移除 yqdz 字段
- API结构重构

### 界面优化 🎨
- 菜单重构和名称修改
- 客户列表样式优化
- 顶部栏样式调整
- 凭证表格对齐优化
- 样式调整和文案修改

### 功能修复 🐛
- 修复切换公司后，凭证页不显示问题
- 修复回单修改场景
- 修复税率处理逻辑
- 凭证列表重复刷新bug修复
- 合计大写问题修复
- 写入状态异常修复
- AI助手发送按钮重复发送消息问题修复
- 修复热重载重复注册方法问题
- 读取config配置bug修复

### 性能优化 ⚡
- 凭证列表使用虚拟列表实现，优化大数据量下渲染性能
- 整理项目结构
- hooks整理
- 编辑凭证页面代码整理

### 业务逻辑 💼
- 修改AI助手银行回单和发票处理完成后跳转逻辑
- 切换场景时需要选择对应原始凭证类型的记账场景
- 增加税款所属期

---

**说明**: 
- 🆕 表示新功能
- 🚀 表示功能改进
- 🐛 表示问题修复
- ❌ 表示移除功能
- 📝 表示文档更新

项目基于 Vue 3 + Ant Design Vue + TypeScript 技术栈构建。

